import { reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import * as yup from 'yup'

export interface WorkflowData {
	name: string
	process_group: any
	description: string
}

export interface WorkflowVersionData {
	scope_use: any[]
	followers: any[]
	process_manager: any[]
	job_manager: any[]
}

export function useWorkflowForm() {
	const { t } = useI18n()

	// Initial data structures
	const createInitialWorkflowData = (): WorkflowData => ({
		name: '',
		process_group: null,
		description: ''
	})

	const createInitialWorkflowVersionData = (): WorkflowVersionData => ({
		scope_use: [],
		followers: [],
		process_manager: [],
		job_manager: []
	})

	// Validation schema for workflow
	const createWorkflowValidationSchema = () => {
		return yup.object().shape({
			workflow_name: yup.string()
				.required(`${t('workflow.name')} ${t('workflow.validate.required')}`),
			process_manager: yup.array()
				.min(1, `${t('workflow.process_manager')} ${t('workflow.validate.required')}`)
				.required(`${t('workflow.process_manager')} ${t('workflow.validate.required')}`),
			job_manager: yup.array()
				.min(1, `${t('workflow.job_manager')} ${t('workflow.validate.required')}`)
				.required(`${t('workflow.job_manager')} ${t('workflow.validate.required')}`),
		})
	}

	// State management
	const workflowState = reactive({
		dataWorkflow: createInitialWorkflowData(),
		dataWorkflowVersion: createInitialWorkflowVersionData(),
		isLoading: false,
		errors: {} as Record<string, string>
	})

	// Helper functions
	const resetWorkflowData = () => {
		Object.assign(workflowState.dataWorkflow, createInitialWorkflowData())
		Object.assign(workflowState.dataWorkflowVersion, createInitialWorkflowVersionData())
		workflowState.errors = {}
	}

	const validateWorkflowData = async () => {
		try {
			const schema = createWorkflowValidationSchema()
			await schema.validate({
				workflow_name: workflowState.dataWorkflow.name,
				process_manager: workflowState.dataWorkflowVersion.process_manager,
				job_manager: workflowState.dataWorkflowVersion.job_manager
			}, { abortEarly: false })
			
			workflowState.errors = {}
			return true
		} catch (error: any) {
			if (error.inner) {
				workflowState.errors = error.inner.reduce((acc: Record<string, string>, err: any) => {
					acc[err.path] = err.message
					return acc
				}, {})
			}
			return false
		}
	}

	const setWorkflowData = (data: Partial<WorkflowData>) => {
		Object.assign(workflowState.dataWorkflow, data)
	}

	const setWorkflowVersionData = (data: Partial<WorkflowVersionData>) => {
		Object.assign(workflowState.dataWorkflowVersion, data)
	}

	// Process group modal state
	const processGroupModalState = reactive({
		showModal: false,
		showComponent: false,
		data: {
			name: ''
		}
	})

	const openProcessGroupModal = () => {
		processGroupModalState.showModal = true
		processGroupModalState.showComponent = true
	}

	const closeProcessGroupModal = () => {
		processGroupModalState.showModal = false
		processGroupModalState.showComponent = false
	}

	const resetProcessGroupModal = () => {
		processGroupModalState.data = { name: '' }
		processGroupModalState.showComponent = true
	}

	return {
		// State
		workflowState,
		processGroupModalState,
		
		// Data creators
		createInitialWorkflowData,
		createInitialWorkflowVersionData,
		
		// Validation
		createWorkflowValidationSchema,
		validateWorkflowData,
		
		// Data management
		resetWorkflowData,
		setWorkflowData,
		setWorkflowVersionData,
		
		// Process group modal
		openProcessGroupModal,
		closeProcessGroupModal,
		resetProcessGroupModal
	}
}

export default useWorkflowForm
