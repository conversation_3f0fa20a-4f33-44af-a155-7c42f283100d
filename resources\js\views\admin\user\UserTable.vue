<template>
    <CTable align="middle" responsive>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th class="align-middle">
                        {{ $t('user.account_name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.full_name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.role') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.email') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.department') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.rank') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.job_position') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.create_by') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('user.created_at') }}
                    </th>
                </tr>
            </thead>
            <tbody v-if="checkDataNotEmpty">
                <tr 
                    v-for="(user, index) in dataUsers" 
                    :key="index"
                    @contextmenu="handleContextMenu($event, user, index)"
                    @touchstart="startPress($event, user, index)"
                    @touchend="endPress"
                >
                    <td class="align-middle">
                        {{ user.account_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ user.full_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ getDisplayableRoles(user.roles) }}
                    </td>
                    <td class="align-middle">
                        {{ user.email || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ user.department?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ user.rank?.name || $t('common.no_data') }}
                    </td>
					<td class="align-middle">
						{{ user.job_position?.name || $t('common.no_data') }}
					</td>
                    <td class="align-middle">
                        {{ user?.create_by?.full_name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ user.created_at ? formatDate(user.created_at) : $t('common.no_data') }}
                    </td>
                </tr>
            </tbody>
            <tbody v-else>
                <tr>
                    <td colspan="9" class="align-middle text-center">
                        {{ $t('search.no_matching_records_found') }}
                    </td>
                </tr>
            </tbody>
        </table>
    </CTable>
    <Teleport to="body">
        <ContextMenu
            v-if="isContextMenuVisible" 
            :options="contextMenuOptions"
            :isVisible="isContextMenuVisible"
            :position="contextMenuPosition"
            @selectOption="handleOption"
        />
    </Teleport>

    <!-- User Role Modal -->
    <UserRole
        :visible="showUserRoleModal"
        :userData="selectedUser"
        @close="closeUserRoleModal"
        @updated="handleUserUpdated"
    />
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import moment from 'moment'
import { USER_STATUS } from '@/constants/constants';
import { useI18n } from 'vue-i18n';
import ContextMenu from '@/views/action/ContextMenu.vue';
import UserRole from '@/views/admin/user/UserRole.vue';
import { useContextMenu } from '@/composables/contextMenu';

export default defineComponent({
    name: 'UserTable',
    emits: ['refresh-data'],

    components: {
        ContextMenu,
        UserRole,
    },

    props: {
        dataUsers: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, { emit }) {
        const { t } = useI18n();

        // Modal state
        const showUserRoleModal = ref(false);
        const selectedUser = ref<any>(null);

        const checkDataNotEmpty = computed<boolean>(() => {
            return props.dataUsers.length > 0;
        });
        const formatDate = (date: string) => {
            return moment(date).format('DD/MM/YYYY');
        };

        const getDisplayableRoles = (roles: any[] | undefined) => {
            if (!roles || roles.length === 0) {
                return t('common.no_data');
            }
            const firstRoleName = roles[0]?.name;
            if (roles.length === 1) {
                return firstRoleName || t('common.no_data');
            }
            return `${firstRoleName || t('common.no_data')} (+${roles.length - 1})`;
        };

        // Các options cho context menu của component này
		const contextMenuOptions = computed(() => {
			let menuOptions: any = [];
			const menuOptionUser = [
				{ label: t('menu_options.user.role'), action: 'UPDATE_ROLE', icon: 'group' },
			];

			menuOptions = [...menuOptionUser];

			return menuOptions;
		});

        // Sử dụng composable useContextMenu
		const {
			isContextMenuVisible, 
			contextMenuPosition, 
			selectedField, 
			showContextMenu, 
			startPress, 
			endPress 
		} = useContextMenu();

        const handleOption = (option: any) => {
			if (option.action === 'UPDATE_ROLE') {
				handleUpdateRoleOption();
			}
		};

        // Hiển thị context menu và lưu field được chọn
		const handleContextMenu = (event: any, field: any, index: number) => {
			showContextMenu(event, field, index);
		};

        const handleUpdateRoleOption = () => {
			if (selectedField.value) {
				selectedUser.value = selectedField.value;
				showUserRoleModal.value = true;
			}
		};

		const closeUserRoleModal = () => {
			showUserRoleModal.value = false;
			selectedUser.value = null;
		};

		const handleUserUpdated = () => {
			emit('refresh-data');
		};

        return {
            checkDataNotEmpty,
            formatDate,
            getDisplayableRoles,
            contextMenuOptions,
            isContextMenuVisible,
            contextMenuPosition,
            selectedField,
            showContextMenu,
            startPress,
            endPress,
            handleOption,
            handleContextMenu,
            showUserRoleModal,
            selectedUser,
            closeUserRoleModal,
            handleUserUpdated,
        }
    },
});
</script>
<style scoped>

</style>