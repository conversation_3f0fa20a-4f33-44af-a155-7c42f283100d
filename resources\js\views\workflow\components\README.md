# Workflow Form Components

<PERSON><PERSON><PERSON> là tài liệu hướng dẫn sử dụng các component tái sử dụng được tách ra từ `WorkflowAdd.vue`.

## Cấu trúc Components

### 1. WorkflowForm.vue
Component này chứa form thông tin cơ bản của workflow:
- **name**: Tên workflow (bắt buộc)
- **process_group**: Nhóm quy trình
- **description**: Mô tả

#### Props:
```typescript
{
  dataWorkflow: {
    name: string,
    process_group: any,
    description: string
  },
  showModalProcessGroup: boolean,
  showComponentModal: boolean,
  dataProcessGroup: object,
  debouncedGetOptionProcessGroups: Function
}
```

#### Events:
- `update:dataWorkflow`: Cập nhật dữ liệu workflow
- `create-process-group`: Tạo process group mới
- `reset-modal-process-group`: Reset modal process group
- `hide-modal-process-group`: Ẩn modal process group

### 2. WorkflowVersionForm.vue
Component này chứa form thông tin phân quyền của workflow:
- **scope_use**: Phạm vi sử dụng
- **followers**: Người theo dõi
- **process_manager**: Quản lý quy trình (bắt buộc)
- **job_manager**: Quản lý công việc (bắt buộc)

#### Props:
```typescript
{
  dataWorkflowVersion: {
    scope_use: any[],
    followers: any[],
    process_manager: any[],
    job_manager: any[]
  },
  debouncedGetOptionScopes: Function
}
```

#### Events:
- `update:dataWorkflowVersion`: Cập nhật dữ liệu workflow version

### 3. useWorkflowForm.ts
Composable quản lý logic và state chung cho workflow form:

#### Exported functions:
- `createInitialWorkflowData()`: Tạo dữ liệu workflow ban đầu
- `createInitialWorkflowVersionData()`: Tạo dữ liệu workflow version ban đầu
- `createWorkflowValidationSchema()`: Tạo schema validation
- `validateWorkflowData()`: Validate dữ liệu
- `resetWorkflowData()`: Reset dữ liệu
- `openProcessGroupModal()`: Mở modal process group
- `closeProcessGroupModal()`: Đóng modal process group
- `resetProcessGroupModal()`: Reset modal process group

## Cách sử dụng

### 1. Import components và composable:
```typescript
import WorkflowForm from '@/views/workflow/components/WorkflowForm.vue'
import WorkflowVersionForm from '@/views/workflow/components/WorkflowVersionForm.vue'
import { useWorkflowForm } from '@/composables/useWorkflowForm'
```

### 2. Sử dụng trong component:
```vue
<template>
  <div>
    <!-- Form thông tin cơ bản -->
    <WorkflowForm 
      v-model:dataWorkflow="workflowData"
      :showModalProcessGroup="showModal"
      :showComponentModal="showComponent"
      :dataProcessGroup="processGroupData"
      @create-process-group="handleCreateProcessGroup"
      @reset-modal-process-group="handleResetModal"
      @hide-modal-process-group="handleHideModal"
      :debouncedGetOptionProcessGroups="getProcessGroups"
    />
    
    <!-- Form phân quyền -->
    <WorkflowVersionForm 
      v-model:dataWorkflowVersion="workflowVersionData"
      :debouncedGetOptionScopes="getScopes"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useWorkflowForm } from '@/composables/useWorkflowForm'

// Sử dụng composable
const {
  workflowState,
  processGroupModalState,
  validateWorkflowData,
  resetWorkflowData,
  openProcessGroupModal,
  closeProcessGroupModal,
  resetProcessGroupModal
} = useWorkflowForm()

// Local state
const workflowData = ref(workflowState.dataWorkflow)
const workflowVersionData = ref(workflowState.dataWorkflowVersion)

// Event handlers
const handleCreateProcessGroup = () => {
  openProcessGroupModal()
}

const handleResetModal = () => {
  resetProcessGroupModal()
}

const handleHideModal = () => {
  closeProcessGroupModal()
}
</script>
```

## Validation

Components sử dụng vee-validate cho validation. Schema validation được định nghĩa trong composable:

```typescript
const schema = createWorkflowValidationSchema()
```

Các trường bắt buộc:
- `workflow_name`: Tên workflow
- `process_manager`: Quản lý quy trình (ít nhất 1 item)
- `job_manager`: Quản lý công việc (ít nhất 1 item)

## Demo

Xem file `WorkflowFormDemo.vue` để có ví dụ đầy đủ về cách sử dụng các component.

## Lợi ích

1. **Tái sử dụng**: Có thể sử dụng trong nhiều component khác nhau
2. **Tách biệt logic**: Logic form được tách riêng vào composable
3. **Dễ bảo trì**: Code được tổ chức rõ ràng, dễ debug và maintain
4. **Type safety**: Sử dụng TypeScript cho type safety
5. **Validation tập trung**: Validation logic được quản lý tập trung

## Migration từ WorkflowAdd.vue

Để migrate từ code cũ:

1. Thay thế phần template form bằng các component mới
2. Import và sử dụng composable `useWorkflowForm`
3. Cập nhật event handlers để sử dụng functions từ composable
4. Đảm bảo validation vẫn hoạt động đúng

Tất cả tính năng hiện tại được giữ nguyên, chỉ tổ chức code tốt hơn.
