<template>
	<div class="container mt-4">
		<h2>Demo: Workflow Form Components</h2>
		
		<div class="card mb-4">
			<div class="card-header">
				<h4>WorkflowForm Component</h4>
			</div>
			<div class="card-body">
				<WorkflowForm 
					v-model:dataWorkflow="workflowData"
					:showModalProcessGroup="showModal"
					:showComponentModal="showComponent"
					:dataProcessGroup="processGroupData"
					@create-process-group="handleCreateProcessGroup"
					@reset-modal-process-group="handleResetModal"
					@hide-modal-process-group="handleHideModal"
					:debouncedGetOptionProcessGroups="mockGetProcessGroups"
				/>
				
				<div class="mt-3">
					<h5>Current Data:</h5>
					<pre>{{ JSON.stringify(workflowData, null, 2) }}</pre>
				</div>
			</div>
		</div>

		<div class="card mb-4">
			<div class="card-header">
				<h4>WorkflowVersionForm Component</h4>
			</div>
			<div class="card-body">
				<WorkflowVersionForm 
					v-model:dataWorkflowVersion="workflowVersionData"
					:debouncedGetOptionScopes="mockGetScopes"
				/>
				
				<div class="mt-3">
					<h5>Current Data:</h5>
					<pre>{{ JSON.stringify(workflowVersionData, null, 2) }}</pre>
				</div>
			</div>
		</div>

		<div class="card">
			<div class="card-header">
				<h4>Composable State</h4>
			</div>
			<div class="card-body">
				<button @click="resetData" class="btn btn-secondary me-2">Reset Data</button>
				<button @click="validateData" class="btn btn-primary">Validate</button>
				
				<div class="mt-3" v-if="Object.keys(errors).length > 0">
					<h5>Validation Errors:</h5>
					<div class="alert alert-danger">
						<ul class="mb-0">
							<li v-for="(error, field) in errors" :key="field">
								{{ field }}: {{ error }}
							</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import WorkflowForm from './WorkflowForm.vue'
import WorkflowVersionForm from './WorkflowVersionForm.vue'
import { useWorkflowForm } from '@/composables/useWorkflowForm'

export default defineComponent({
	name: 'WorkflowFormDemo',
	components: {
		WorkflowForm,
		WorkflowVersionForm
	},
	setup() {
		const { t } = useI18n()
		
		// Use the composable
		const {
			workflowState,
			validateWorkflowData,
			resetWorkflowData
		} = useWorkflowForm()

		// Local state for demo
		const workflowData = ref({
			name: '',
			process_group: null,
			description: ''
		})

		const workflowVersionData = ref({
			scope_use: [],
			followers: [],
			process_manager: [],
			job_manager: []
		})

		const showModal = ref(false)
		const showComponent = ref(false)
		const processGroupData = ref({ name: '' })
		const errors = ref({})

		// Mock functions
		const mockGetProcessGroups = async (query: string) => {
			return [
				{ value: 1, label: 'Process Group 1' },
				{ value: 2, label: 'Process Group 2' },
				{ value: 3, label: 'Process Group 3' }
			].filter(item => item.label.toLowerCase().includes(query.toLowerCase()))
		}

		const mockGetScopes = async (query: string) => {
			return [
				{ value: 1, label: 'Admin', description: 'Administrator role' },
				{ value: 2, label: 'Manager', description: 'Manager role' },
				{ value: 3, label: 'User', description: 'Regular user role' }
			].filter(item => item.label.toLowerCase().includes(query.toLowerCase()))
		}

		// Event handlers
		const handleCreateProcessGroup = () => {
			showModal.value = true
			showComponent.value = true
		}

		const handleResetModal = () => {
			processGroupData.value = { name: '' }
			showComponent.value = true
		}

		const handleHideModal = () => {
			showModal.value = false
			showComponent.value = false
		}

		const resetData = () => {
			workflowData.value = {
				name: '',
				process_group: null,
				description: ''
			}
			workflowVersionData.value = {
				scope_use: [],
				followers: [],
				process_manager: [],
				job_manager: []
			}
			errors.value = {}
		}

		const validateData = async () => {
			// Update composable state with current data
			workflowState.dataWorkflow = workflowData.value
			workflowState.dataWorkflowVersion = workflowVersionData.value
			
			const isValid = await validateWorkflowData()
			errors.value = workflowState.errors
			
			if (isValid) {
				alert('Validation passed!')
			}
		}

		return {
			workflowData,
			workflowVersionData,
			showModal,
			showComponent,
			processGroupData,
			errors,
			mockGetProcessGroups,
			mockGetScopes,
			handleCreateProcessGroup,
			handleResetModal,
			handleHideModal,
			resetData,
			validateData
		}
	}
})
</script>

<style scoped>
.container {
	max-width: 1200px;
}

pre {
	background-color: #f8f9fa;
	padding: 1rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
}
</style>
