<template>
	<CRow class="mb-2">
		<CCol :xs="4">
			<div class="d-flex align-items-center">
				<label class="mb-1">
					{{ $t('workflow.scope_use') }}
				</label>
				<span 
					class="material-symbols-outlined ms-1 cursor-pointer icon-info"
					v-b-tooltip.hover
					:title="$t('workflow.scope_use_desc')"
				>
					info
				</span>
			</div>
			<Multiselect
				mode="tags"
				v-model="localDataWorkflowVersion.scope_use" 
				:placeholder="$t('workflow.choose')"
				:close-on-select="false"
				:filter-results="false"
				:resolve-on-load="false"
				:infinite="true"
				:limit="20"
				:clear-on-search="true"
				:searchable="true"
				:delay="0"
				:min-chars="0"
				:object="true"
				:options="async (query) => {
					return await debouncedGetOptionScopes(query)
				}"
				@open="debouncedGetOptionScopes('')"
				:can-clear="false"
			>
				<template v-slot:option="{ option }">
					<div class="custom-option">
						<div class="option-label mb-1">
							{{ option.label }}
						</div>
						<div class="option-description text-secondary">
							<small>
								<i>{{ option.description }}</i>
							</small>
						</div>
					</div>
				</template>
			</Multiselect>
		</CCol>
		<CCol :xs="4">
			<label class="mb-1">
				{{ $t('workflow.followers') }}
			</label>
			<Multiselect
				mode="tags"
				v-model="localDataWorkflowVersion.followers" 
				:placeholder="$t('workflow.choose')"
				:close-on-select="false"
				:filter-results="false"
				:resolve-on-load="false"
				:infinite="true"
				:limit="20"
				:clear-on-search="true"
				:searchable="true"
				:delay="0"
				:min-chars="0"
				:object="true"
				:options="async (query) => {
					return await debouncedGetOptionScopes(query)
				}"
				@open="debouncedGetOptionScopes('')"
				:can-clear="false"
			>
				<template v-slot:option="{ option }">
					<div class="custom-option">
						<div class="option-label mb-1">
							{{ option.label }}
						</div>
						<div class="option-description text-secondary">
							<small>
								<i>{{ option.description }}</i>
							</small>
						</div>
					</div>
				</template>
			</Multiselect>
		</CCol>
	</CRow>
	<CRow class="mb-2">
		<CCol :xs="4">
			<div class="d-flex align-items-center">
				<label class="mb-1">
					{{ $t('workflow.process_manager') }}
					<span class="text-danger">*</span>
				</label>
				<span 
					class="material-symbols-outlined ms-1 cursor-pointer icon-info"
					v-b-tooltip.hover
					:title="$t('workflow.process_manager_desc')"
				>
					info
				</span>
			</div>
			<Field 
				name="process_manager"
				v-slot="{ field }"
			>
				<Multiselect
					mode="tags"
					v-bind="field"
					v-model="localDataWorkflowVersion.process_manager" 
					:placeholder="$t('workflow.choose')"
					:close-on-select="false"
					:filter-results="false"
					:resolve-on-load="false"
					:infinite="true"
					:limit="20"
					:clear-on-search="true"
					:searchable="true"
					:delay="0"
					:min-chars="0"
					:object="true"
					:options="async (query) => {
						return await debouncedGetOptionScopes(query)
					}"
					@open="debouncedGetOptionScopes('')"
					:can-clear="false"
				>
					<template v-slot:option="{ option }">
						<div class="custom-option">
							<div class="option-label mb-1">
								{{ option.label }}
							</div>
							<div class="option-description text-secondary">
								<small>
									<i>{{ option.description }}</i>
								</small>
							</div>
						</div>
					</template>
				</Multiselect>
			</Field>
			<ErrorMessage
				as="div"
				name="process_manager"
				class="text-danger"
			/>
		</CCol>
		<CCol :xs="4">
			<div class="d-flex align-items-center">
				<label class="mb-1">
					{{ $t('workflow.job_manager') }}
					<span class="text-danger">*</span>
				</label>
				<span 
					class="material-symbols-outlined ms-1 cursor-pointer icon-info"
					v-b-tooltip.hover
					:title="$t('workflow.job_manager_desc')"
				>
					info
				</span>
			</div>
			<Field 
				name="job_manager"
				v-slot="{ field }"
			>
				<Multiselect
					mode="tags"
					v-bind="field"
					v-model="localDataWorkflowVersion.job_manager" 
					:placeholder="$t('workflow.choose')"
					:close-on-select="false"
					:filter-results="false"
					:resolve-on-load="false"
					:infinite="true"
					:limit="20"
					:clear-on-search="true"
					:searchable="true"
					:delay="0"
					:min-chars="0"
					:object="true"
					:options="async (query) => {
						return await debouncedGetOptionScopes(query)
					}"
					@open="debouncedGetOptionScopes('')"
					:can-clear="false"
				>
					<template v-slot:option="{ option }">
						<div class="custom-option">
							<div class="option-label mb-1">
								{{ option.label }}
							</div>
							<div class="option-description text-secondary">
								<small>
									<i>{{ option.description }}</i>
								</small>
							</div>
						</div>
					</template>
				</Multiselect>
			</Field>
			<ErrorMessage
				as="div"
				name="job_manager"
				class="text-danger"
			/>
		</CCol>
	</CRow>
</template>

<script lang="ts">
import { defineComponent, computed, PropType } from 'vue'
import { useI18n } from "vue-i18n";
import { Field, ErrorMessage } from 'vee-validate';
import Multiselect from '@vueform/multiselect';

export default defineComponent({
	name: 'WorkflowVersionForm',
	components: {
		Field,
		ErrorMessage,
		Multiselect
	},
	props: {
		dataWorkflowVersion: {
			type: Object as PropType<any>,
			required: true,
			default: () => ({
				scope_use: [],
				followers: [],
				process_manager: [],
				job_manager: []
			})
		},
		debouncedGetOptionScopes: {
			type: Function,
			required: true
		}
	},
	emits: [
		'update:dataWorkflowVersion'
	],
	setup(props, { emit }) {
		const { t } = useI18n();

		// Computed properties for two-way binding
		const localDataWorkflowVersion = computed({
			get: () => props.dataWorkflowVersion,
			set: (value) => emit('update:dataWorkflowVersion', value)
		});

		return {
			t,
			localDataWorkflowVersion
		};
	}
});
</script>

<style scoped>
.cursor-pointer {
	cursor: pointer;
}

.icon-info {
	color: #6c757d;
}

.custom-option {
	padding: 8px 0;
}

.option-label {
	font-weight: 500;
}

.option-description {
	font-size: 0.875rem;
}
</style>
