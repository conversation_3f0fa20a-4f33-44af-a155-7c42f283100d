<template>
	<CModal
		:visible="isVisible"
		@close="handleClose"
		size="lg"
		backdrop="static"
		keyboard
	>
		<CModalHeader>
			<CModalTitle>{{ $t('workflow.update_info.title') }}</CModalTitle>
		</CModalHeader>
		<CModalBody>
			<Form ref="form" @submit="handleSubmitWorkflow" :validation-schema="schema('WORKFLOW')">
				<WorkflowForm
					v-model:dataWorkflow="state.dataWorkflow"
					:showModalProcessGroup="processGroupModalState.showModal"
					:showComponentModal="processGroupModalState.showComponent"
					:dataProcessGroup="processGroupModalState.data"
					@create-process-group="createProcessGroup"
					@reset-modal-process-group="resetModalProcessGroup"
					@hide-modal-process-group="hideModalProcessGroup"
					:debouncedGetOptionProcessGroups="debouncedGetOptionProcessGroups"
				/>
			</Form>
		</CModalBody>
		<CModalFooter>
			<CButton color="secondary" @click="handleClose">
				{{ $t('common.cancel') }}
			</CButton>
			<CButton color="primary" @click="handleSubmitWorkflow">
				{{ $t('common.save') }}
			</CButton>
		</CModalFooter>
	</CModal>
</template>

<script lang="ts">
import { defineComponent, reactive, watch, PropType } from 'vue';
import { Form } from 'vee-validate';
import { useI18n } from 'vue-i18n';
import WorkflowForm from '@/views/workflow/components/WorkflowForm.vue';
import useWorkflows from '@/composables/workflow';
import useWorkflowForm from '@/composables/useWorkflowForm';
import useOptions from '@/composables/option';
import { debounce } from 'lodash';
import * as yup from 'yup';

export default defineComponent({
	name: 'WorkflowUpdateInfo',
	components: {
		Form,
		WorkflowForm
	},
	props: {
		isVisible: {
			type: Boolean,
			default: false
		},
		workflowData: {
			type: Object as PropType<any>,
			default: () => null
		}
	},
	emits: ['close', 'updated'],
	setup(props, { emit }) {
		const { t } = useI18n();
		const { setIsLoading } = useWorkflows();
		const { getProcessGroups } = useOptions();
		const {
			workflowState,
			processGroupModalState,
			openProcessGroupModal,
			closeProcessGroupModal,
			resetProcessGroupModal
		} = useWorkflowForm();

		// Schema validation function
		const schema = (typeIsRequired: string) => {
			let schemaForm = yup.object().shape({});

			if (typeIsRequired == "WORKFLOW") {
				schemaForm = schemaForm.shape({
					workflow_name: yup.string()
						.required(`${t('workflow.name')} ${t('workflow.validate.required')}`),
				});
			}

			return schemaForm;
		};

		// Mock updateWorkflow function - replace with actual API call
		const updateWorkflow = async (id: number, data: any) => {
			// This should be replaced with actual API call
			console.log('Updating workflow:', id, data);
			return { status: 'success', data: data };
		};

		// Process group options
		const getOptionProcessGroups = async (query: string) => {
			let result = await getProcessGroups(query);
			if (Array.isArray(result) && result.length > 0) {
				return result.map((elem: any) => ({
					value: elem.id,
					label: elem.name,
				}));
			}
			return [];
		};

		const debouncedGetOptionProcessGroups = debounce(getOptionProcessGroups, 500);

		// Event handlers for WorkflowForm
		const createProcessGroup = () => {
			openProcessGroupModal();
		};

		const resetModalProcessGroup = () => {
			resetProcessGroupModal();
		};

		const hideModalProcessGroup = () => {
			closeProcessGroupModal();
		};

		const state = reactive({
			dataWorkflow: {
				name: '',
				process_group: null,
				description: ''
			}
		});

		// Watch for changes in workflowData prop to populate form
		watch(() => props.workflowData, (newData) => {
			if (newData) {
				state.dataWorkflow = {
					name: newData.name || '',
					process_group: newData.process_group || null,
					description: newData.description || ''
				};
			}
		}, { immediate: true });

		const handleClose = () => {
			emit('close');
		};

		const handleSubmitWorkflow = async () => {
			try {
				if (props.workflowData?.id) {
					const result = await updateWorkflow(props.workflowData.id, state.dataWorkflow);
					if (result.status === 'success') {
						emit('updated', result);
						handleClose();
					}
				}
			} catch (error) {
				console.error('Error updating workflow:', error);
			}
		};

		return {
			t,
			schema,
			state,
			processGroupModalState,
			createProcessGroup,
			resetModalProcessGroup,
			hideModalProcessGroup,
			debouncedGetOptionProcessGroups,
			handleClose,
			handleSubmitWorkflow,
			setIsLoading
		};
	}
});
</script>

<style scoped>
</style>