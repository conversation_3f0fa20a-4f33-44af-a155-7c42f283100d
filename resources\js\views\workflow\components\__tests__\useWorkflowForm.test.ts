import { describe, it, expect, beforeEach } from 'vitest'
import { createI18n } from 'vue-i18n'
import { useWorkflowForm } from '@/composables/useWorkflowForm'

// Mock i18n
const i18n = createI18n({
  legacy: false,
  locale: 'vi',
  messages: {
    vi: {
      workflow: {
        name: 'Tên workflow',
        process_manager: 'Quản lý quy trình',
        job_manager: 'Quản lý công việc',
        validate: {
          required: 'là bắt buộc'
        }
      }
    }
  }
})

// Mock useI18n
vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: (key: string) => {
      const keys = key.split('.')
      let value: any = i18n.global.messages.value.vi
      for (const k of keys) {
        value = value?.[k]
      }
      return value || key
    }
  })
}))

describe('useWorkflowForm', () => {
  let composable: ReturnType<typeof useWorkflowForm>

  beforeEach(() => {
    composable = useWorkflowForm()
  })

  it('creates initial workflow data correctly', () => {
    const initialData = composable.createInitialWorkflowData()
    
    expect(initialData).toEqual({
      name: '',
      process_group: null,
      description: ''
    })
  })

  it('creates initial workflow version data correctly', () => {
    const initialData = composable.createInitialWorkflowVersionData()
    
    expect(initialData).toEqual({
      scope_use: [],
      followers: [],
      process_manager: [],
      job_manager: []
    })
  })

  it('initializes state correctly', () => {
    expect(composable.workflowState.dataWorkflow).toEqual({
      name: '',
      process_group: null,
      description: ''
    })
    
    expect(composable.workflowState.dataWorkflowVersion).toEqual({
      scope_use: [],
      followers: [],
      process_manager: [],
      job_manager: []
    })
    
    expect(composable.workflowState.isLoading).toBe(false)
    expect(composable.workflowState.errors).toEqual({})
  })

  it('resets workflow data correctly', () => {
    // Modify the data first
    composable.workflowState.dataWorkflow.name = 'Test'
    composable.workflowState.dataWorkflowVersion.scope_use = [1, 2, 3]
    composable.workflowState.errors = { name: 'Error' }
    
    // Reset
    composable.resetWorkflowData()
    
    expect(composable.workflowState.dataWorkflow.name).toBe('')
    expect(composable.workflowState.dataWorkflowVersion.scope_use).toEqual([])
    expect(composable.workflowState.errors).toEqual({})
  })

  it('sets workflow data correctly', () => {
    composable.setWorkflowData({ name: 'Test Workflow' })
    
    expect(composable.workflowState.dataWorkflow.name).toBe('Test Workflow')
  })

  it('sets workflow version data correctly', () => {
    composable.setWorkflowVersionData({ scope_use: [1, 2] })
    
    expect(composable.workflowState.dataWorkflowVersion.scope_use).toEqual([1, 2])
  })

  it('manages process group modal state correctly', () => {
    expect(composable.processGroupModalState.showModal).toBe(false)
    expect(composable.processGroupModalState.showComponent).toBe(false)
    
    composable.openProcessGroupModal()
    expect(composable.processGroupModalState.showModal).toBe(true)
    expect(composable.processGroupModalState.showComponent).toBe(true)
    
    composable.closeProcessGroupModal()
    expect(composable.processGroupModalState.showModal).toBe(false)
    expect(composable.processGroupModalState.showComponent).toBe(false)
  })

  it('resets process group modal correctly', () => {
    composable.processGroupModalState.data.name = 'Test'
    
    composable.resetProcessGroupModal()
    
    expect(composable.processGroupModalState.data.name).toBe('')
    expect(composable.processGroupModalState.showComponent).toBe(true)
  })

  it('validates workflow data correctly', async () => {
    // Test with empty required fields
    const isValid1 = await composable.validateWorkflowData()
    expect(isValid1).toBe(false)
    expect(Object.keys(composable.workflowState.errors).length).toBeGreaterThan(0)
    
    // Test with valid data
    composable.workflowState.dataWorkflow.name = 'Test Workflow'
    composable.workflowState.dataWorkflowVersion.process_manager = [{ id: 1 }]
    composable.workflowState.dataWorkflowVersion.job_manager = [{ id: 1 }]
    
    const isValid2 = await composable.validateWorkflowData()
    expect(isValid2).toBe(true)
    expect(composable.workflowState.errors).toEqual({})
  })
})
