<template>
	<div class="container mt-4">
		<h2>Demo: Workflow Update Info Modal</h2>
		
		<div class="card mb-4">
			<div class="card-header">
				<h4>Test WorkflowUpdateInfo Modal</h4>
			</div>
			<div class="card-body">
				<div class="mb-3">
					<h5>Sample Workflow Data:</h5>
					<pre>{{ JSON.stringify(sampleWorkflowData, null, 2) }}</pre>
				</div>
				
				<div class="mb-3">
					<button 
						class="btn btn-primary me-2" 
						@click="showModal"
					>
						Open Update Info Modal
					</button>
					
					<button 
						class="btn btn-secondary" 
						@click="resetData"
					>
						Reset Data
					</button>
				</div>
				
				<div v-if="lastUpdateResult" class="alert alert-success">
					<h6>Last Update Result:</h6>
					<pre>{{ JSON.stringify(lastUpdateResult, null, 2) }}</pre>
				</div>
			</div>
		</div>
		
		<!-- WorkflowUpdateInfo Modal -->
		<WorkflowUpdateInfo
			:isVisible="isModalVisible"
			:workflowData="sampleWorkflowData"
			@close="handleCloseModal"
			@updated="handleWorkflowUpdated"
		/>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import WorkflowUpdateInfo from './WorkflowUpdateInfo.vue';

export default defineComponent({
	name: 'WorkflowUpdateInfoDemo',
	components: {
		WorkflowUpdateInfo
	},
	setup() {
		const isModalVisible = ref(false);
		const lastUpdateResult = ref(null);
		
		const sampleWorkflowData = ref({
			id: 1,
			name: 'Sample Workflow',
			description: 'This is a sample workflow for testing',
			process_group: {
				id: 1,
				label: 'Sample Process Group',
				value: 1
			},
			status: 'active',
			created_at: '2024-01-01',
			updated_at: '2024-01-15',
			created_by: {
				name: 'John Doe'
			},
			updated_by: {
				name: 'Jane Smith'
			}
		});

		const showModal = () => {
			isModalVisible.value = true;
		};

		const handleCloseModal = () => {
			isModalVisible.value = false;
		};

		const handleWorkflowUpdated = (result: any) => {
			lastUpdateResult.value = result;
			console.log('Workflow updated in demo:', result);
		};

		const resetData = () => {
			sampleWorkflowData.value = {
				id: 1,
				name: 'Sample Workflow',
				description: 'This is a sample workflow for testing',
				process_group: {
					id: 1,
					label: 'Sample Process Group',
					value: 1
				},
				status: 'active',
				created_at: '2024-01-01',
				updated_at: '2024-01-15',
				created_by: {
					name: 'John Doe'
				},
				updated_by: {
					name: 'Jane Smith'
				}
			};
			lastUpdateResult.value = null;
		};

		return {
			isModalVisible,
			sampleWorkflowData,
			lastUpdateResult,
			showModal,
			handleCloseModal,
			handleWorkflowUpdated,
			resetData
		};
	}
});
</script>

<style scoped>
.container {
	max-width: 1200px;
}

pre {
	background-color: #f8f9fa;
	padding: 1rem;
	border-radius: 0.375rem;
	font-size: 0.875rem;
}

.alert pre {
	background-color: transparent;
	margin-bottom: 0;
}
</style>
