import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import WorkflowForm from '../WorkflowForm.vue'

// Mock i18n
const i18n = createI18n({
  legacy: false,
  locale: 'vi',
  messages: {
    vi: {
      workflow: {
        name: 'Tên workflow',
        process_group: 'Nhóm quy trình',
        description: 'Mô tả',
        choose: 'Chọn'
      },
      process_group: {
        create: 'Tạo nhóm quy trình'
      }
    }
  }
})

describe('WorkflowForm', () => {
  const defaultProps = {
    dataWorkflow: {
      name: '',
      process_group: null,
      description: ''
    },
    showModalProcessGroup: false,
    showComponentModal: false,
    dataProcessGroup: { name: '' },
    debouncedGetOptionProcessGroups: vi.fn().mockResolvedValue([])
  }

  it('renders correctly', () => {
    const wrapper = mount(WorkflowForm, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('input[name="workflow_name"]').exists()).toBe(true)
    expect(wrapper.find('textarea[name="description"]').exists()).toBe(true)
  })

  it('emits update:dataWorkflow when input changes', async () => {
    const wrapper = mount(WorkflowForm, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const nameInput = wrapper.find('input[name="workflow_name"]')
    await nameInput.setValue('Test Workflow')

    // Check if the component would emit the update
    expect(wrapper.vm.localDataWorkflow.name).toBe('Test Workflow')
  })

  it('emits create-process-group when add button is clicked', async () => {
    const wrapper = mount(WorkflowForm, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const addButton = wrapper.find('.material-symbols-outlined')
    await addButton.trigger('click')

    expect(wrapper.emitted('create-process-group')).toBeTruthy()
  })

  it('displays validation error message', () => {
    const wrapper = mount(WorkflowForm, {
      props: defaultProps,
      global: {
        plugins: [i18n],
        stubs: {
          ErrorMessage: {
            template: '<div class="error-message">{{ name }} is required</div>',
            props: ['name']
          }
        }
      }
    })

    expect(wrapper.find('.error-message').exists()).toBe(true)
  })

  it('handles process group modal correctly', () => {
    const wrapper = mount(WorkflowForm, {
      props: {
        ...defaultProps,
        showModalProcessGroup: true,
        showComponentModal: true
      },
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.vm.localShowModalProcessGroup).toBe(true)
  })
})
