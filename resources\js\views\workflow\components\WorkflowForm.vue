<template>
	<CRow class="mb-2">
		<CCol :xs="4">
			<label class="mb-1">
				{{ $t('workflow.name') }}
				<span class="text-danger">*</span>
			</label>
			<Field 
				v-model="localDataWorkflow.name" 
				name="workflow_name"
				type="text" 
				class="form-control" 
				:placeholder="$t('workflow.name')"
				maxlength="200" 
			/>
			<ErrorMessage
				as="div"
				name="workflow_name"
				class="text-danger"
			/>
		</CCol>
		<CCol :xs="4">
			<div class="d-flex align-items-center">
				<label class="mb-1">
					{{ $t('workflow.process_group') }} 
				</label>
				<span 
					@click="$emit('create-process-group')" 
					class="material-symbols-outlined ms-1 cursor-pointer"
					v-b-tooltip.hover
					:title="$t('process_group.create')"
				>
					add_circle
				</span>
				<BModal 
					size="md" 
					hide-footer
					no-close-on-backdrop
					no-close-on-esc
					centered 
					:title="$t('process_group.create')"
					v-model="localShowModalProcessGroup"
					@hidden="$emit('reset-modal-process-group')"
					@show="$emit('reset-modal-process-group')"
				>
					<div v-if="showComponentModal">
						<process-group-add
							:dataProcessGroup="dataProcessGroup"
							@close-modal-process-group="$emit('hide-modal-process-group')"
						>
						</process-group-add>
					</div>
				</BModal>
			</div>
			{{ localDataWorkflow.process_group }}
			<Multiselect
				v-model="localDataWorkflow.process_group" 
				:placeholder="$t('workflow.choose')"
				:close-on-select="false"
				:filter-results="false"
				:resolve-on-load="false"
				:infinite="true"
				:limit="10"
				:clear-on-search="true"
				:searchable="true"
				:delay="0"
				:min-chars="0"
				:object="true"
				:options="async (query) => {
					return await debouncedGetOptionProcessGroups(query)
				}"
				@open="debouncedGetOptionProcessGroups('')"
				:can-clear="false"
			/>
		</CCol>
	</CRow>
	<CRow class="mb-2">
		<CCol :xs="8">
			<label class="mb-1">
				{{ $t('workflow.description') }}
			</label>
			<Field 
				v-model="localDataWorkflow.description" 
				name="description"
				as="textarea"
				class="form-control" 
				:placeholder="$t('workflow.description')"
				maxlength="500" 
				rows="2"
			/>
		</CCol>
	</CRow>
</template>

<script lang="ts">
import { defineComponent, computed, PropType } from 'vue'
import { useI18n } from "vue-i18n";
import { Field, ErrorMessage } from 'vee-validate';
import Multiselect from '@vueform/multiselect';
import ProcessGroupAdd from '@/views/workflow/process-group/ProcessGroupAdd.vue';

export default defineComponent({
	name: 'WorkflowForm',
	components: {
		Field,
		ErrorMessage,
		Multiselect,
		ProcessGroupAdd
	},
	props: {
		dataWorkflow: {
			type: Object as PropType<any>,
			required: true,
			default: () => ({
				name: '',
				process_group: null,
				description: ''
			})
		},
		showModalProcessGroup: {
			type: Boolean,
			default: false
		},
		showComponentModal: {
			type: Boolean,
			default: false
		},
		dataProcessGroup: {
			type: Object as PropType<any>,
			required: true
		},
		debouncedGetOptionProcessGroups: {
			type: Function,
			required: true
		}
	},
	emits: [
		'update:dataWorkflow',
		'create-process-group',
		'reset-modal-process-group',
		'hide-modal-process-group'
	],
	setup(props, { emit }) {
		const { t } = useI18n();

		// Computed properties for two-way binding
		const localDataWorkflow = computed({
			get: () => props.dataWorkflow,
			set: (value) => emit('update:dataWorkflow', value)
		});

		const localShowModalProcessGroup = computed({
			get: () => props.showModalProcessGroup,
			set: (value) => {
				// This will be handled by parent component
			}
		});

		return {
			t,
			localDataWorkflow,
			localShowModalProcessGroup
		};
	}
});
</script>

<style scoped>
.cursor-pointer {
	cursor: pointer;
}
</style>
