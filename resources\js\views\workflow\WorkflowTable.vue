<template>
    <CTable align="middle" responsive>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th class="align-middle">
                        {{ $t('workflow.name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.status') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.process_version') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.process_group') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.created_by') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.created_at') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.updated_at') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.updated_by') }}
                    </th>
                </tr>
            </thead>
            <tbody v-if="checkDataNotEmpty">
                <tr 
                    v-for="(workflow, index) in dataWorkflows" 
                    :key="index"
                    @contextmenu="handleContextMenu($event, workflow, index)"
                    @touchstart="startPress($event, workflow, index)"
                    @touchend="endPress"
                >
                    <td class="align-middle">
                        <router-link :to="{ name: 'WorkflowDetail', params: { id: workflow.id } }" class="text-decoration-none">
                            {{ workflow?.name || $t('common.no_data') }}
                        </router-link>
                    </td>
                    <td class="align-middle">
                        <span class="badge rounded-pill bg-danger" v-if="workflow.status == WORKFLOWS.STATUS.UNACTIVE">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_workflow.unactive') }}
                            </small>
                        </span>
                        <span class="badge rounded-pill bg-success" v-else-if="workflow.status == WORKFLOWS.STATUS.ACTIVE">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_workflow.active') }}
                            </small>
                        </span>
                    </td>
                    <td class="align-middle">
                        <span class="text-dark fw-bold" v-if="workflow.status == WORKFLOWS.STATUS.SAVE_DRAFT">
                            {{ $t('option_tab_workflow.save_draft') }}
                        </span>
                        <span class="text-success fw-bold" v-else>
                            {{ $t('workflow.use_real') }}
                        </span>
                    </td>
                    <td class="align-middle">
                        {{ workflow?.process_group?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.created_by?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.created_at ? formatDate(workflow?.created_at) : $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.updated_at ? formatDate(workflow?.updated_at) : $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.updated_by?.name || $t('common.no_data') }}
                    </td>
                </tr>
            </tbody>
            <tbody v-else>
                <tr>
                    <td colspan="10" class="align-middle text-center">
                        {{ $t('search.no_matching_records_found') }}
                    </td>
                </tr>
            </tbody>
        </table>
    </CTable>

    <!-- User Role Modal -->
    <WorkflowUpdateInfo
        
    />

    <Teleport to="body">
        <ContextMenu
            v-if="isContextMenuVisible" 
            :options="contextMenuOptions"
            :isVisible="isContextMenuVisible"
            :position="contextMenuPosition"
            @selectOption="handleOption"
        />
    </Teleport>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { WORKFLOWS } from '@/constants/constants';
import moment from 'moment';
import { useI18n } from 'vue-i18n';
import ContextMenu from '@/views/action/ContextMenu.vue';
import { useContextMenu } from '@/composables/contextMenu';
import { useRouter } from 'vue-router';
import { useRoute } from 'vue-router';
import WorkflowUpdateInfo from '@/views/workflow/WorkflowUpdateInfo.vue';

export default defineComponent({
    name: 'WorkflowTable',
    emits: ['update-data-paginate'],

    components: {
        ContextMenu,
        WorkflowUpdateInfo
    },

    props: {
        dataWorkflows: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const { t } = useI18n();
        const router = useRouter();
        const route = useRoute();

        const checkDataNotEmpty = computed<boolean>(() => {
            return props.dataWorkflows.length > 0;
        });

        const formatDate = (date: string) => {
            return moment(date).format('DD/MM/YYYY');
        };

        // Các options cho context menu của component này
		const contextMenuOptions = computed(() => {
			let menuOptions: any = [];
			const menuOptionWorkflow = [
				{ label: t('menu_options.workflow.update_setting'), action: 'UPDATE_SETTING_WORKFLOW', icon: 'edit' },
                { label: t('menu_options.workflow.update_info'), action: 'UPDATE_INFO_WORKFLOW', icon: 'info' },
			];

			menuOptions = [...menuOptionWorkflow];

			return menuOptions;
		});

        // Sử dụng composable useContextMenu
		const {
			isContextMenuVisible, 
			contextMenuPosition, 
			selectedField, 
			showContextMenu, 
			startPress, 
			endPress 
		} = useContextMenu();

        const handleOption = (option: any) => {
			if (option.action === 'UPDATE_SETTING_WORKFLOW') {
				handleUpdateSettingWorkflowOption();
			} else if (option.action === 'UPDATE_INFO_WORKFLOW') {
				handleUpdateInfoWorkflowOption();
			}
		};

        // Hiển thị context menu và lưu field được chọn
		const handleContextMenu = (event: any, field: any, index: number) => {
			showContextMenu(event, field, index);
		};

        const handleUpdateSettingWorkflowOption = async () => {
            if (selectedField.value) {
				await router.push({ name: 'WorkflowUpdateSetting', params: { id: selectedField.value.id} });
			}
		};

        const handleUpdateInfoWorkflowOption = async () => {
            
		};

        return {
            WORKFLOWS,
            checkDataNotEmpty,
            formatDate,
            contextMenuOptions,
            isContextMenuVisible,
            contextMenuPosition,
            selectedField,
            showContextMenu,
            startPress,
            endPress,
            handleOption,
            handleContextMenu,
        }
    },
});
</script>
<style type="text/css" scoped>
</style>